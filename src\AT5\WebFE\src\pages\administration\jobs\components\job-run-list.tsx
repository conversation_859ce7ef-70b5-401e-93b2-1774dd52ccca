import { createRTKODataGridDataSource } from '@/store/api/odata-grid-data-source';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { selectFilterModel, selectPage, selectPageSize, selectSortModel, setFilterModel, setPage, setPageSize, setSortModel } from '@/store/slices/job-runs-slice';

import { DataGridPremium, GridColDef, useGridApiRef } from '@mui/x-data-grid-premium';
import React from 'react';
import { administrationApi } from '@/store/api/administration';
import { useNavigate } from 'react-router-dom';
import { paths } from '@/paths';
import { toast } from '@/components/core/toaster';

export function JobRunList(): React.JSX.Element {
    const apiRef = useGridApiRef();
    const dispatch = useAppDispatch();
    const pageSize = useAppSelector(selectPageSize);
    const page = useAppSelector(selectPage);
    const sortModel = useAppSelector(selectSortModel);
    const filterModel = useAppSelector(selectFilterModel);
    const navigate = useNavigate();

    const [dataSource, setDataSource] = React.useState(() => createRTKODataGridDataSource(
        administrationApi.endpoints.atApiServiceEndpointsV2AdministrationJobsGetJobRunsEndpoint.initiate
        ,
        () => {
            const current = apiRef.current;
            if (!current) return undefined;
            // Collect visible, non-action column fields
            const visible = current.getVisibleColumns?.() ?? [];
            return visible
                .map((c: any) => c.field)
                // Exclude action and internal utility columns like checkbox selection
                .filter((f: string) => f && f !== 'actions' && !f.startsWith('__'));
        }
    ));

    interface JobRunRow {
        id: number,
        triggered: string | null;
        started: string | null;
        finished: string | null;
        jobName: string;
        authorId?: number;
    }

    const columns : GridColDef<JobRunRow>[] = [
        {
            field: 'id',
            headerName: 'Id',
            minWidth: 90,
            align: 'center',
            headerAlign: 'center',
            type: 'number',
        },
        {
            field: 'triggered',
            headerName: 'Triggered',
            minWidth: 220,
            align: 'center',
            headerAlign: 'center',
            type: 'dateTime',
            valueGetter: (value) => value ? new Date(value) : null,
        },
        {
            field: 'started',
            headerName: 'Started',
            minWidth: 220,
            type: 'dateTime',
            valueGetter: (value) => value ? new Date(value) : null,
        },
        {
            field: 'finished',
            headerName: 'Finished',
            minWidth: 220,
            type: 'dateTime',
            valueGetter: (value) => value ? new Date(value) : null,
        },
        {
            field: 'jobName',
            headerName: 'Job Name',
            minWidth: 200,
            type: 'string',
        },
        {
            field: 'authorId',
            headerName: 'Author Id',
            width: 90,
            type: 'number',
        },
    ];

    return (
        <DataGridPremium<JobRunRow>
            apiRef={apiRef}
            columns={columns}
            dataSource={dataSource}
            filterMode='server'
            getRowId={(row) => row.id}
            onRowDoubleClick={(params) => {
                navigate(paths.administration.jobs.jobRuns.view(params.row.id))
            }}
            onDataSourceError={(error) => {
                toast(`DataGrid dataSource error: ${error}`);
            }}
            showToolbar={true}
            disablePivoting
            filterDebounceMs={500}
            ignoreDiacritics
            pagination
            pageSizeOptions={[10, 20, 50, 100]}
            paginationModel={{ pageSize, page }}
            onPaginationModelChange={(model) => {
                if (model.pageSize !== pageSize) {
                    dispatch(setPageSize(model.pageSize));
                }
                if (model.page !== page) {
                    dispatch(setPage(model.page));
                }
            }}
            sortModel={sortModel}
            onSortModelChange={(model) => {
                dispatch(setSortModel(model.map(item => ({
                    field: item.field,
                    sort: item.sort || null
                }))));
            }}
            filterModel={filterModel}
            onFilterModelChange={(model) => {
                dispatch(setFilterModel(model));
            }}
            hideFooterSelectedRowCount
        />
    );
}